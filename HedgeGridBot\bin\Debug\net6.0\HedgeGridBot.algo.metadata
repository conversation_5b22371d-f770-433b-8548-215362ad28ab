[{"Flags": "Robot", "Guid": "00000000-0000-0000-0000-000000000000", "Group": "Custom", "FriendlyName": "HedgeGridBot", "ShortName": null, "TypeName": "cAlgo.Robots.HedgeGridBot", "AssemblyName": "HedgeGridBot, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null", "TimeZone": "UTC", "IsOverlay": false, "IsPercentage": false, "ScalePrecision": null, "Levels": [], "DefaultSymbolName": null, "DefaultTimeFrame": null, "Video": null, "Lines": [], "Clouds": [], "Parameters": [{"MinValue": -1.7976931348623157e+308, "MaxValue": 1.7976931348623157e+308, "Step": 0.1, "DefaultValue": 1.1, "ParameterType": "Double", "PropertyName": "RangeHigh", "FriendlyName": "Range High", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": -1.7976931348623157e+308, "MaxValue": 1.7976931348623157e+308, "Step": 0.1, "DefaultValue": 1.09, "ParameterType": "Double", "PropertyName": "RangeLow", "FriendlyName": "Range Low", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 2, "MaxValue": 2147483647, "Step": 1, "DefaultValue": 10, "ParameterType": "Integer", "PropertyName": "GridDivisions", "FriendlyName": "Grid Divisions", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 0.01, "MaxValue": 1.7976931348623157e+308, "Step": 0.1, "DefaultValue": 0.01, "ParameterType": "Double", "PropertyName": "Volume", "FriendlyName": "Volume (Lots)", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 1, "MaxValue": 2147483647, "Step": 1, "DefaultValue": 3, "ParameterType": "Integer", "PropertyName": "MaxGridLevelCross", "FriendlyName": "Max Grid Level Cross", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 0.0, "MaxValue": 1.7976931348623157e+308, "Step": 0.1, "DefaultValue": 5.0, "ParameterType": "Double", "PropertyName": "StopPipsBuffer", "FriendlyName": "Stop Pips Buffer", "GroupName": null, "IsValueVisibleInTitle": true}], "Capabilities": [], "CustomAttributes": [], "Sets": [], "AddIndicatorsToChart": true, "AdditionalInfoUrl": null}]