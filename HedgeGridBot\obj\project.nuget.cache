{"version": 2, "dgSpecHash": "3v9g4jKu2RVp4o2rqCEc/U9v8FQG5dg74bj8A4JloFJIRzbmBp7uMFg8g4uO5f+GEUG6VGFrKL+OewEsJGe8WA==", "success": true, "projectFilePath": "C:\\Users\\<USER>\\OneDrive\\Documents\\cAlgo\\Sources\\Robots\\HedgeGridBot\\HedgeGridBot\\HedgeGridBot.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\ctrader.automate\\1.0.12\\ctrader.automate.1.0.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.ref\\6.0.22\\microsoft.netcore.app.ref.6.0.22.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.ref\\6.0.22\\microsoft.windowsdesktop.app.ref.6.0.22.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.ref\\6.0.22\\microsoft.aspnetcore.app.ref.6.0.22.nupkg.sha512"], "logs": []}