{"version": 3, "targets": {"net6.0": {"cTrader.Automate/1.0.12": {"type": "package", "compile": {"lib/net6.0/cAlgo.API.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/cAlgo.API.dll": {"related": ".xml"}}, "build": {"build/cTrader.Automate.props": {}, "build/cTrader.Automate.targets": {}}}}}, "libraries": {"cTrader.Automate/1.0.12": {"sha512": "h+HPnbqPTkoSeojoYwWWOo5Jm9ExkmrmsU/WbF/idcgO2auKAPILBghZRP6Qt5mAcE/40YCRMNHT45/kzzs5Sg==", "type": "package", "path": "ctrader.automate/1.0.12", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/cTrader.Automate.props", "build/cTrader.Automate.targets", "ctrader.automate.1.0.12.nupkg.sha512", "ctrader.automate.nuspec", "eula.md", "icon.png", "lib/net40/cAlgo.API.dll", "lib/net40/cAlgo.API.xml", "lib/net6.0/cAlgo.API.dll", "lib/net6.0/cAlgo.API.xml", "tools/net472/Core.AlgoFormat.Compose.Reflection.dll", "tools/net472/Core.AlgoFormat.Writer.dll", "tools/net472/Core.AlgoFormat.dll", "tools/net472/Core.Domain.Primitives.dll", "tools/net472/Newtonsoft.Json.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Reflection.MetadataLoadContext.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/cTrader.Automate.Sdk.Tasks.dll", "tools/net6.0/Core.AlgoFormat.Compose.Reflection.dll", "tools/net6.0/Core.AlgoFormat.Writer.dll", "tools/net6.0/Core.AlgoFormat.dll", "tools/net6.0/Core.Connection.Protobuf.Common.dll", "tools/net6.0/Core.Domain.Primitives.dll", "tools/net6.0/Microsoft.Win32.SystemEvents.dll", "tools/net6.0/Newtonsoft.Json.dll", "tools/net6.0/System.Drawing.Common.dll", "tools/net6.0/System.Reflection.MetadataLoadContext.dll", "tools/net6.0/System.Security.Permissions.dll", "tools/net6.0/System.Windows.Extensions.dll", "tools/net6.0/cTrader.Automate.Sdk.Tasks.dll", "tools/net6.0/protobuf-net.Core.dll", "tools/net6.0/protobuf-net.dll", "tools/net6.0/runtimes/unix/lib/net6.0/System.Drawing.Common.dll", "tools/net6.0/runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "tools/net6.0/runtimes/win/lib/net6.0/System.Drawing.Common.dll", "tools/net6.0/runtimes/win/lib/net6.0/System.Windows.Extensions.dll"]}}, "projectFileDependencyGroups": {"net6.0": ["cTrader.Automate >= *"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Documents\\cAlgo\\Sources\\Robots\\HedgeGridBot\\HedgeGridBot\\HedgeGridBot.csproj", "projectName": "HedgeGridBot", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\cAlgo\\Sources\\Robots\\HedgeGridBot\\HedgeGridBot\\HedgeGridBot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\cAlgo\\Sources\\Robots\\HedgeGridBot\\HedgeGridBot\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"cTrader.Automate": {"target": "Package", "version": "[*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.22, 6.0.22]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.22, 6.0.22]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.22, 6.0.22]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100-rc.1.23455.8\\RuntimeIdentifierGraph.json"}}}}