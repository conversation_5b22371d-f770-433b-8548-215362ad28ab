using System;
using System.Collections.Generic;
using System.Linq;
using cAlgo.API;
using cAlgo.API.Collections;
using cAlgo.API.Indicators;
using cAlgo.API.Internals;

namespace cAlgo.Robots
{
    [Robot(AccessRights = AccessRights.None, AddIndicators = true)]
    public class HedgeGridBot : Robot
    {
        // Input Parameters
        [Parameter("Range High", DefaultValue = 1.1000)]
        public double RangeHigh { get; set; }

        [Parameter("Range Low", DefaultValue = 1.0900)]
        public double RangeLow { get; set; }

        [Parameter("Grid Divisions", DefaultValue = 10, MinValue = 2)]
        public int GridDivisions { get; set; }

        [Parameter("Volume (Lots)", DefaultValue = 0.01, MinValue = 0.01)]
        public double Volume { get; set; }

        [Parameter("Max Grid Level Cross", DefaultValue = 3, MinValue = 1)]
        public int MaxGridLevelCross { get; set; }

        [Parameter("Stop Pips Buffer", DefaultValue = 5, MinValue = 0)]
        public double StopPipsBuffer { get; set; }

        [Parameter("Max Runtime Bars", DefaultValue = 100, MinValue = 1)]
        public int MaxRuntimeBars { get; set; }

        // Private fields
        private List<double> gridLevels;
        private Dictionary<double, bool> gridCrossed;
        private List<ChartHorizontalLine> gridLines;
        private double gridSpacing;
        private int startBarIndex;
        private bool isRuntimeExpired;
        private string GRID_LINE_PREFIX;
        private string BUY_LABEL_PREFIX;
        private string SELL_LABEL_PREFIX;

        protected override void OnStart()
        {
            // Validate parameters
            if (RangeHigh <= RangeLow)
            {
                Print("Error: Range High must be greater than Range Low");
                Stop();
                return;
            }

            // Initialize unique prefixes with symbol and timeframe
            string uniqueId = $"{SymbolName}_{TimeFrame}";
            GRID_LINE_PREFIX = $"GridLine_{uniqueId}_";
            BUY_LABEL_PREFIX = $"GridBuy_{uniqueId}_";
            SELL_LABEL_PREFIX = $"GridSell_{uniqueId}_";

            // Initialize collections
            gridLevels = new List<double>();
            gridCrossed = new Dictionary<double, bool>();
            gridLines = new List<ChartHorizontalLine>();

            // Initialize runtime tracking
            startBarIndex = Bars.Count - 1;
            isRuntimeExpired = false;

            // Calculate grid levels and spacing
            CalculateGridLevels();

            // Draw grid lines on chart
            DrawGridLines();

            // Subscribe to position events
            Positions.Opened += OnPositionOpened;
            Positions.Closed += OnPositionClosed;

            Print($"HedgeGridBot started with {GridDivisions} grid levels between {RangeLow} and {RangeHigh}");
            Print($"Grid spacing: {gridSpacing:F5} ({gridSpacing / Symbol.PipSize:F1} pips)");
            Print($"Bot will automatically stop after {MaxRuntimeBars} bars from start (Current bar: {startBarIndex})");
            Print($"Bot will automatically stop after {MaxRuntimeBars} bars from start (Current bar: {startBarIndex})");
        }

        private void CalculateGridLevels()
        {
            gridSpacing = (RangeHigh - RangeLow) / GridDivisions;

            for (int i = 0; i <= GridDivisions; i++)
            {
                double level = RangeLow + (i * gridSpacing);
                gridLevels.Add(level);
                gridCrossed[level] = false;
            }

            // Sort grid levels to ensure proper order
            gridLevels.Sort();
        }

        private void DrawGridLines()
        {
            for (int i = 0; i < gridLevels.Count; i++)
            {
                string lineName = GRID_LINE_PREFIX + i;
                var line = Chart.DrawHorizontalLine(lineName, gridLevels[i], Color.Blue);
                line.Thickness = 1;
                line.LineStyle = LineStyle.Solid;
                gridLines.Add(line);
            }
        }

        protected override void OnBar()
        {
            // Check if runtime has expired (only on new bars)
            if (!isRuntimeExpired)
            {
                CheckRuntimeExpiration();
            }
        }

        protected override void OnTick()
        {
            // Only continue trading if runtime hasn't expired
            if (!isRuntimeExpired)
            {
                CheckForGridCrosses();
                ManageOpenPositions();
            }
        }

        private void CheckRuntimeExpiration()
        {
            int currentBarIndex = Bars.Count - 1;
            int barsElapsed = currentBarIndex - startBarIndex;

            if (barsElapsed >= MaxRuntimeBars)
            {
                isRuntimeExpired = true;
                Print($"Runtime expired after {barsElapsed} bars. Closing all positions and stopping bot...");

                CloseAllGridPositions();
                Stop();
            }
        }

        private void CloseAllGridPositions()
        {
            var gridPositions = Positions.Where(p => p.SymbolName == SymbolName &&
                (p.Label.StartsWith(BUY_LABEL_PREFIX) || p.Label.StartsWith(SELL_LABEL_PREFIX))).ToList();

            foreach (var position in gridPositions)
            {
                var result = ClosePosition(position);
                if (result.IsSuccessful)
                {
                    Print($"Closed position {position.Label} due to runtime expiration. Profit: {position.NetProfit:F2}");
                }
                else
                {
                    Print($"Failed to close position {position.Label}: {result.Error}");
                }
            }

            Print($"Closed {gridPositions.Count} grid positions due to runtime expiration.");
        }

        private void CheckForGridCrosses()
        {
            double currentPrice = Bars.LastBar.Close;

            foreach (double gridLevel in gridLevels)
            {
                if (!gridCrossed[gridLevel] && Math.Abs(currentPrice - gridLevel) <= Symbol.TickSize)
                {
                    gridCrossed[gridLevel] = true;
                    OpenHedgePositions(gridLevel);
                }
            }
        }

        private void OpenHedgePositions(double gridLevel)
        {
            // Find grid level index
            int gridIndex = gridLevels.IndexOf(gridLevel);

            // Check if positions already exist at this grid level
            string buyLabel = BUY_LABEL_PREFIX + gridIndex;
            string sellLabel = SELL_LABEL_PREFIX + gridIndex;

            bool buyPositionExists = Positions.Any(p => p.SymbolName == SymbolName && p.Label == buyLabel);
            bool sellPositionExists = Positions.Any(p => p.SymbolName == SymbolName && p.Label == sellLabel);

            // If both positions already exist, skip opening new ones
            if (buyPositionExists && sellPositionExists)
            {
                Print($"Both positions already exist at grid level {gridIndex} ({gridLevel:F5}) - skipping");
                return;
            }

            double volumeInUnits = Symbol.QuantityToVolumeInUnits(Volume);

            // Calculate take profit levels
            double buyTakeProfit = gridIndex < gridLevels.Count - 1 ? gridLevels[gridIndex + 1] : 0;
            double sellTakeProfit = gridIndex > 0 ? gridLevels[gridIndex - 1] : 0;

            // Calculate stop loss levels with buffer
            double buyStopLoss = CalculateStopLoss(gridLevel, TradeType.Buy, gridIndex);
            double sellStopLoss = CalculateStopLoss(gridLevel, TradeType.Sell, gridIndex);

            // Open Buy position only if it doesn't exist and TP is valid
            if (!buyPositionExists && buyTakeProfit > 0)
            {
                var buyResult = ExecuteMarketOrder(TradeType.Buy, SymbolName, volumeInUnits, buyLabel,
                    buyStopLoss, buyTakeProfit, $"Grid Buy at level {gridIndex}");

                buyResult.Position.ModifyStopLossPrice();
                buyResult.Position.ModifyTakeProfitPrice();

                if (buyResult.IsSuccessful)
                {
                    Print($"Opened BUY position at grid level {gridIndex} (Price: {gridLevel:F5})");
                }
                else
                {
                    Print($"Failed to open BUY position: {buyResult.Error}");
                }
            }
            else if (buyPositionExists)
            {
                Print($"BUY position already exists at grid level {gridIndex} - skipping");
            }

            // Open Sell position only if it doesn't exist and TP is valid
            if (!sellPositionExists && sellTakeProfit > 0)
            {
                var sellResult = ExecuteMarketOrder(TradeType.Sell, SymbolName, volumeInUnits, sellLabel,
                    sellStopLoss, sellTakeProfit, $"Grid Sell at level {gridIndex}");

                if (sellResult.IsSuccessful)
                {
                    Print($"Opened SELL position at grid level {gridIndex} (Price: {gridLevel:F5})");
                }
                else
                {
                    Print($"Failed to open SELL position: {sellResult.Error}");
                }
            }
            else if (sellPositionExists)
            {
                Print($"SELL position already exists at grid level {gridIndex} - skipping");
            }
        }

        private double CalculateStopLoss(double gridLevel, TradeType tradeType, int gridIndex)
        {
            double baseStopPrice;

            if (tradeType == TradeType.Buy)
            {
                // For buy trades: base stop is the higher of (gridLevel - MaxGridLevelCross * gridSpacing) and RangeLow
                double maxCrossStop = gridLevel - (MaxGridLevelCross * gridSpacing);
                baseStopPrice = Math.Max(maxCrossStop, RangeLow);

                // Apply buffer: final stop = base stop - buffer
                return baseStopPrice - (StopPipsBuffer * Symbol.PipSize);
            }
            else
            {
                // For sell trades: base stop is the lower of (gridLevel + MaxGridLevelCross * gridSpacing) and RangeHigh
                double maxCrossStop = gridLevel + (MaxGridLevelCross * gridSpacing);
                baseStopPrice = Math.Min(maxCrossStop, RangeHigh);

                // Apply buffer: final stop = base stop + buffer
                return baseStopPrice + (StopPipsBuffer * Symbol.PipSize);
            }
        }

        private void ManageOpenPositions()
        {
            // Check all open positions for this symbol
            var positions = Positions.Where(p => p.SymbolName == SymbolName &&
                (p.Label.StartsWith(BUY_LABEL_PREFIX) || p.Label.StartsWith(SELL_LABEL_PREFIX))).ToList();

            foreach (var position in positions)
            {
                // Check if position has reached take profit or stop loss
                // The platform handles this automatically, but we can add custom logic here if needed

                // Optional: Add trailing stop logic or other position management features
                CheckPositionForManualClose(position);
            }
        }

        private void CheckPositionForManualClose(Position position)
        {
            // Extract grid index from position label
            string gridIndexStr = position.Label.Replace(BUY_LABEL_PREFIX, "").Replace(SELL_LABEL_PREFIX, "");
            if (int.TryParse(gridIndexStr, out int gridIndex))
            {
                if (gridIndex >= 0 && gridIndex < gridLevels.Count)
                {
                    // Use appropriate price for each trade type
                    double currentPrice = position.TradeType == TradeType.Buy ? Symbol.Bid : Symbol.Ask;

                    // Check if position should be closed based on custom logic
                    // For now, we rely on the automatic TP/SL handling

                    // Optional: Add custom exit conditions here
                    // Example: Close if price moves too far from entry
                    double maxAllowedDistance = (MaxGridLevelCross + 1) * gridSpacing;
                    if (Math.Abs(currentPrice - position.EntryPrice) > maxAllowedDistance)
                    {
                        ClosePosition(position);
                        Print($"Closed position {position.Label} due to excessive distance from entry");
                    }
                }
            }
        }



        private void OnPositionOpened(PositionOpenedEventArgs args)
        {
            var position = args.Position;
            if (position.SymbolName == SymbolName &&
                (position.Label.StartsWith(BUY_LABEL_PREFIX) || position.Label.StartsWith(SELL_LABEL_PREFIX)))
            {
                Print($"Position opened: {position.Label} | Entry: {position.EntryPrice:F5} | " +
                      $"SL: {position.StopLoss:F5} | TP: {position.TakeProfit:F5}");
            }
        }

        private void OnPositionClosed(PositionClosedEventArgs args)
        {
            var position = args.Position;
            if (position.SymbolName == SymbolName &&
                (position.Label.StartsWith(BUY_LABEL_PREFIX) || position.Label.StartsWith(SELL_LABEL_PREFIX)))
            {
                Print($"Position closed: {position.Label} | Profit: {position.NetProfit:F2} | " +
                      $"Pips: {position.Pips:F1} | Reason: {args.Reason}");

                // Reset grid crossed status to allow new trades at this level
                ResetGridCrossedStatus(position.Label);
            }
        }

        private void ResetGridCrossedStatus(string positionLabel)
        {
            // Extract grid index from position label
            string gridIndexStr = positionLabel.Replace(BUY_LABEL_PREFIX, "").Replace(SELL_LABEL_PREFIX, "");
            if (int.TryParse(gridIndexStr, out int gridIndex))
            {
                if (gridIndex >= 0 && gridIndex < gridLevels.Count)
                {
                    double gridLevel = gridLevels[gridIndex];
                    gridCrossed[gridLevel] = false;
                    Print($"Reset grid crossed status for level {gridIndex} ({gridLevel:F5})");
                }
            }
        }

        protected override void OnStop()
        {
            // Unsubscribe from position events
            Positions.Opened -= OnPositionOpened;
            Positions.Closed -= OnPositionClosed;

            // Clean up chart objects
            for (int i = 0; i < gridLevels.Count; i++)
            {
                string lineName = GRID_LINE_PREFIX + i;
                Chart.RemoveObject(lineName);
            }

            Print("HedgeGridBot stopped and chart objects cleaned up");
        }
    }
}