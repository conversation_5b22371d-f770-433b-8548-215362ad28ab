{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Documents\\cAlgo\\Sources\\Robots\\HedgeGridBot\\HedgeGridBot\\HedgeGridBot.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Documents\\cAlgo\\Sources\\Robots\\HedgeGridBot\\HedgeGridBot\\HedgeGridBot.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Documents\\cAlgo\\Sources\\Robots\\HedgeGridBot\\HedgeGridBot\\HedgeGridBot.csproj", "projectName": "HedgeGridBot", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\cAlgo\\Sources\\Robots\\HedgeGridBot\\HedgeGridBot\\HedgeGridBot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\cAlgo\\Sources\\Robots\\HedgeGridBot\\HedgeGridBot\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"cTrader.Automate": {"target": "Package", "version": "[*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.22, 6.0.22]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.22, 6.0.22]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.22, 6.0.22]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100-rc.1.23455.8\\RuntimeIdentifierGraph.json"}}}}}